<script lang="ts" setup>
import Menu from 'primevue/menu'
import type { MenuItem } from 'primevue/menuitem'
import { onBeforeMount, ref } from 'vue'
import { type RouteRecordRaw } from 'vue-router'

import { menuRoutes, type RouteMeta } from '@/router'

async function resolveMenuItems(routes: RouteRecordRaw[], parentPath = '') {
  const items: MenuItem[] = []
  for (const route of routes) {
    const meta: RouteMeta = route.meta || {}

    if (meta.hidden) {
      continue
    }
    if (meta.hasPermission && !(await meta.hasPermission())) {
      continue
    }

    const to = parentPath && route.path ? parentPath + '/' + route.path : parentPath || route.path
    const item: MenuItem = { ...meta, to: to }
    if (route.children) {
      item.items = await resolveMenuItems(route.children, to)
    }
    items.push(item)
  }
  return items
}
const items = ref<MenuItem[]>()
onBeforeMount(() => {
  resolveMenuItems(menuRoutes).then((data) => {
    items.value = data
  })
})

const version = __BUILD_TIME__
</script>

<template>
  <Menu :model="items" :pt="{ root: 'flex flex-col', end: 'mt-auto' }">
    <template #item="{ item, props }">
      <RouterLink v-if="item.to" v-slot="{ href, navigate }" :to="item.to" custom>
        <a :href="href" v-bind="props.action" @click="navigate">
          <span :class="item.icon" />
          <span class="ml-2">{{ item.label }}</span>
        </a>
      </RouterLink>
    </template>

    <template #end>
      <div class="text-surface-600 px-2 py-1 text-xs select-none">版本: {{ version }}</div>
    </template>
  </Menu>
</template>
