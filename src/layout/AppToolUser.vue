<script lang="ts" setup>
import { Menu } from 'primevue'
import <PERSON><PERSON> from 'primevue/button'
import { ref } from 'vue'

import { api } from '@/api'

const menu = ref()
const menuItems = ref([
  {
    label: '退出登录',
    icon: 'pi pi-sign-out',
    command() {
      api.dashAuthTokenRevokeGet({ noAuth: true }).then(() => {
        window.location.reload()
      })
    },
  },
])

function toggle(event: Event) {
  menu.value.toggle(event)
}
</script>

<template>
  <Button
    class="aspect-square"
    icon="pi pi-user"
    variant="outlined"
    severity="secondary"
    @click="toggle"
  ></Button>
  <Menu ref="menu" class="min-w-40!" :model="menuItems" :popup="true"></Menu>
</template>
