<script lang="ts" setup>
import Breadcrumb from 'primevue/breadcrumb'
import type { MenuItem } from 'primevue/menuitem'
import { ref, watch } from 'vue'
import { useRoute, type RouteParamsGeneric } from 'vue-router'

function resolvePath(template: string, params: RouteParamsGeneric): string {
  return template.replace(/:([a-zA-Z_]\w*)/g, (_, key) => {
    if (params[key] == null) {
      throw new Error(`Missing parameter: ${key}`)
    }
    return encodeURIComponent(String(params[key]))
  })
}
const home = ref({
  icon: 'pi pi-home',
})
const route = useRoute()
const breadcumbItems = ref<{ label: string; route?: string }[]>([])
watch(
  () => route.path,
  () => {
    const items: { label: string; route?: string }[] = []
    route.matched.forEach((m) => {
      if (m.meta?.label) {
        const currentPath = resolvePath(m.path, route.params)
        items.push({
          label: (m.meta as MenuItem).label as string,
          route: currentPath === route.path ? undefined : currentPath,
        })
      }
    })
    breadcumbItems.value = items
  },
  { immediate: true },
)
</script>

<template>
  <Breadcrumb :home="home" :model="breadcumbItems">
    <template #item="{ item }">
      <span v-if="item.icon" :class="item.icon"></span>
      <RouterLink v-if="item.route" :to="item.route">
        <span class="hover:font-bold">{{ item.label }}</span>
      </RouterLink>
      <span v-else>{{ item.label }}</span>
    </template>
  </Breadcrumb>
</template>
