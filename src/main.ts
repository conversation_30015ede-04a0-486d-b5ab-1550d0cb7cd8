import './styles.css'
import 'primeicons/primeicons.css'

import Aura from '@primeuix/themes/aura'
import PrimeVue from 'primevue/config'
import ConfirmationService from 'primevue/confirmationservice'
import DialogService from 'primevue/dialogservice'
import ToastService from 'primevue/toastservice'
import { createApp } from 'vue'

import { initializePrimaryColor } from '@/utils/palette'

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(router)

app.use(PrimeVue, {
  theme: {
    preset: Aura,
  },
})
initializePrimaryColor()

app.use(ToastService)
app.use(ConfirmationService)
app.use(DialogService)

app.mount('#app')
