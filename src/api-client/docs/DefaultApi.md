# DefaultApi

All URIs are relative to *https://wallnest.oneproject.dev*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**categoriesColorsGet**](#categoriescolorsget) | **GET** /categories/colors | |
|[**categoriesTagsGet**](#categoriestagsget) | **GET** /categories/tags | |
|[**clientsClientIdTopicsGet**](#clientsclientidtopicsget) | **GET** /clients/{client_id}/topics | |
|[**clientsClientIdTopicsTopicIdWallpapersGet**](#clientsclientidtopicstopicidwallpapersget) | **GET** /clients/{client_id}/topics/{topic_id}/wallpapers | |
|[**imageKeyGet**](#imagekeyget) | **GET** /image/{key} | 获取原始图片|
|[**wallpapersGet**](#wallpapersget) | **GET** /wallpapers | 获取壁纸列表，不包含原始图片地址，所以作为预览展示使用。|
|[**wallpapersKeyRelatedGet**](#wallpaperskeyrelatedget) | **GET** /wallpapers/{key}/related | 获取当前壁纸的相关其他壁纸 (固定数量的)|

# **categoriesColorsGet**
> CategoriesColorsGet200Response categoriesColorsGet()


### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

const { status, data } = await apiInstance.categoriesColorsGet();
```

### Parameters
This endpoint does not have any parameters.


### Return type

**CategoriesColorsGet200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **categoriesTagsGet**
> CategoriesTagsGet200Response categoriesTagsGet()


### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

const { status, data } = await apiInstance.categoriesTagsGet();
```

### Parameters
This endpoint does not have any parameters.


### Return type

**CategoriesTagsGet200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **clientsClientIdTopicsGet**
> ClientsClientIdTopicsGet200Response clientsClientIdTopicsGet()


### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

let clientId: string; // (default to undefined)
let group: string; // (optional) (default to undefined)
let after: number; //使用 client_topic.id，而不是 topic_id (optional) (default to undefined)
let limit: number; // (optional) (default to undefined)

const { status, data } = await apiInstance.clientsClientIdTopicsGet(
    clientId,
    group,
    after,
    limit
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **clientId** | [**string**] |  | defaults to undefined|
| **group** | [**string**] |  | (optional) defaults to undefined|
| **after** | [**number**] | 使用 client_topic.id，而不是 topic_id | (optional) defaults to undefined|
| **limit** | [**number**] |  | (optional) defaults to undefined|


### Return type

**ClientsClientIdTopicsGet200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**400** | Bad Request |  -  |
|**200** | OK |  -  |
|**404** | Not Found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **clientsClientIdTopicsTopicIdWallpapersGet**
> ClientsClientIdTopicsTopicIdWallpapersGet200Response clientsClientIdTopicsTopicIdWallpapersGet()


### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

let clientId: string; // (default to undefined)
let topicId: number; // (default to undefined)
let size: Array<string>; //用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，例如 `1920x1080`。当提供了 `size` 字段后，响应字段 `images` 才出现对应图片地址 (optional) (default to undefined)

const { status, data } = await apiInstance.clientsClientIdTopicsTopicIdWallpapersGet(
    clientId,
    topicId,
    size
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **clientId** | [**string**] |  | defaults to undefined|
| **topicId** | [**number**] |  | defaults to undefined|
| **size** | **Array&lt;string&gt;** | 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址 | (optional) defaults to undefined|


### Return type

**ClientsClientIdTopicsTopicIdWallpapersGet200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **imageKeyGet**
> imageKeyGet()


### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

let key: string; // (default to undefined)
let cFTurnstileResponse: string; // (default to undefined)

const { status, data } = await apiInstance.imageKeyGet(
    key,
    cFTurnstileResponse
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **key** | [**string**] |  | defaults to undefined|
| **cFTurnstileResponse** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: image/*


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**200** | OK |  -  |
|**406** | Not Acceptable |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **wallpapersGet**
> WallpapersGet200Response wallpapersGet()

图片为等比例缩放

### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

let size: Array<string>; //用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，例如 `1920x1080`。当提供了 `size` 字段后，响应字段 `images` 才出现对应图片地址 (optional) (default to undefined)
let page: number; // (optional) (default to undefined)
let pageSize: number; // (optional) (default to undefined)
let sizedFor: 'desktop' | 'mobile'; //[filter] 根据尺寸进行过滤 (optional) (default to undefined)
let color: string; //[filter] 根据色系进行过滤 (optional) (default to undefined)
let tag: string; //[filter] 根据标签进行过滤 (optional) (default to undefined)

const { status, data } = await apiInstance.wallpapersGet(
    size,
    page,
    pageSize,
    sizedFor,
    color,
    tag
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **size** | **Array&lt;string&gt;** | 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址 | (optional) defaults to undefined|
| **page** | [**number**] |  | (optional) defaults to undefined|
| **pageSize** | [**number**] |  | (optional) defaults to undefined|
| **sizedFor** | [**&#39;desktop&#39; | &#39;mobile&#39;**]**Array<&#39;desktop&#39; &#124; &#39;mobile&#39;>** | [filter] 根据尺寸进行过滤 | (optional) defaults to undefined|
| **color** | [**string**] | [filter] 根据色系进行过滤 | (optional) defaults to undefined|
| **tag** | [**string**] | [filter] 根据标签进行过滤 | (optional) defaults to undefined|


### Return type

**WallpapersGet200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **wallpapersKeyRelatedGet**
> WallpapersKeyRelatedGet200Response wallpapersKeyRelatedGet()


### Example

```typescript
import {
    DefaultApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DefaultApi(configuration);

let key: string; //使用 content_md5 值 (default to undefined)
let num: number; //返回的数据量最大条数 (optional) (default to undefined)
let size: Array<string>; //用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，例如 `1920x1080`。当提供了 `size` 字段后，响应字段 `images` 才出现对应图片地址 (optional) (default to undefined)

const { status, data } = await apiInstance.wallpapersKeyRelatedGet(
    key,
    num,
    size
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **key** | [**string**] | 使用 content_md5 值 | defaults to undefined|
| **num** | [**number**] | 返回的数据量最大条数 | (optional) defaults to undefined|
| **size** | **Array&lt;string&gt;** | 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址 | (optional) defaults to undefined|


### Return type

**WallpapersKeyRelatedGet200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

