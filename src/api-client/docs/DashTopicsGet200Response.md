# DashTopicsGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**topics** | [**Array&lt;DashTopicsGet200ResponseTopicsInner&gt;**](DashTopicsGet200ResponseTopicsInner.md) |  | [optional] [default to undefined]
**current_page** | **number** |  | [optional] [default to undefined]
**current_page_size** | **number** |  | [optional] [default to undefined]
**total** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { DashTopicsGet200Response } from './api';

const instance: DashTopicsGet200Response = {
    topics,
    current_page,
    current_page_size,
    total,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
