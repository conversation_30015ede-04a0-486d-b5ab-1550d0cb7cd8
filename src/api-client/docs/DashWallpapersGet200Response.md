# DashWallpapersGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**wallpapers** | [**Array&lt;DashWallpapersGet200ResponseWallpapersInner&gt;**](DashWallpapersGet200ResponseWallpapersInner.md) |  | [optional] [default to undefined]
**current_page** | **number** |  | [optional] [default to undefined]
**current_page_size** | **number** |  | [optional] [default to undefined]
**total** | **number** |  | [optional] [default to undefined]

## Example

```typescript
import { DashWallpapersGet200Response } from './api';

const instance: DashWallpapersGet200Response = {
    wallpapers,
    current_page,
    current_page_size,
    total,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
