# DashUserinfoGet200Response


## Properties

Name | Type | Description | Notes
------------ | ------------- | ------------- | -------------
**email** | **string** |  | [default to undefined]
**is_superuser** | **boolean** | 是否为超级管理员 (如果是超管，则其默认拥有所有权限，不受 permissions 约束) | [default to undefined]
**permissions** | **Array&lt;string&gt;** | 用户拥有的接口权限列表，为 operationId 集合。通过对照接口 operationId 判断是否有权限。 | [default to undefined]

## Example

```typescript
import { DashUserinfoGet200Response } from './api';

const instance: DashUserinfoGet200Response = {
    email,
    is_superuser,
    permissions,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
