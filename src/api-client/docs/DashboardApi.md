# DashboardApi

All URIs are relative to *https://wallnest.oneproject.dev*

|Method | HTTP request | Description|
|------------- | ------------- | -------------|
|[**dashAuthTokenPost**](#dashauthtokenpost) | **POST** /dash/auth/token | |
|[**dashAuthTokenRefreshGet**](#dashauthtokenrefreshget) | **GET** /dash/auth/token/refresh | |
|[**dashAuthTokenRevokeGet**](#dashauthtokenrevokeget) | **GET** /dash/auth/token/revoke | |
|[**dashClientsGet**](#dashclientsget) | **GET** /dash/clients | |
|[**dashSearchWallpapersGet**](#dashsearchwallpapersget) | **GET** /dash/search/wallpapers | |
|[**dashTopicTopicIdPublishClientIdPost**](#dashtopictopicidpublishclientidpost) | **POST** /dash/topic/{topic_id}/publish/{client_id} | 发布专题|
|[**dashTopicpublishedGet**](#dashtopicpublishedget) | **GET** /dash/topicpublished | |
|[**dashTopicpublishedPublishedIdDelete**](#dashtopicpublishedpublishediddelete) | **DELETE** /dash/topicpublished/{published_id} | |
|[**dashTopicpublishedPublishedIdPut**](#dashtopicpublishedpublishedidput) | **PUT** /dash/topicpublished/{published_id} | |
|[**dashTopicsGet**](#dashtopicsget) | **GET** /dash/topics | |
|[**dashTopicsPost**](#dashtopicspost) | **POST** /dash/topics | |
|[**dashTopicsTopicIdDelete**](#dashtopicstopiciddelete) | **DELETE** /dash/topics/{topic_id} | |
|[**dashTopicsTopicIdPut**](#dashtopicstopicidput) | **PUT** /dash/topics/{topic_id} | |
|[**dashTopicsTopicIdSuggestionsTagsGet**](#dashtopicstopicidsuggestionstagsget) | **GET** /dash/topics/{topic_id}/suggestions/tags | 获取 Topic 可能的标签|
|[**dashTopicsTopicIdWallpapersGet**](#dashtopicstopicidwallpapersget) | **GET** /dash/topics/{topic_id}/wallpapers | |
|[**dashTopicsTopicIdWallpapersWallpaperIdDelete**](#dashtopicstopicidwallpaperswallpaperiddelete) | **DELETE** /dash/topics/{topic_id}/wallpapers/{wallpaper_id} | 取消壁纸与话题的关联|
|[**dashTopicsTopicIdWallpapersWallpaperIdPut**](#dashtopicstopicidwallpaperswallpaperidput) | **PUT** /dash/topics/{topic_id}/wallpapers/{wallpaper_id} | 将壁纸与话题关联|
|[**dashUserinfoGet**](#dashuserinfoget) | **GET** /dash/userinfo | |
|[**dashWallpapersGet**](#dashwallpapersget) | **GET** /dash/wallpapers | |
|[**dashWallpapersWallpaperIdTopicsGet**](#dashwallpaperswallpaperidtopicsget) | **GET** /dash/wallpapers/{wallpaper_id}/topics | |
|[**postUploadWallpapers**](#postuploadwallpapers) | **POST** /dash/uploadwallpapers | |

# **dashAuthTokenPost**
> DashAuthTokenPost200Response dashAuthTokenPost()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let cFTurnstileResponse: string; // (default to undefined)
let username: string; // (default to undefined)
let password: string; // (default to undefined)

const { status, data } = await apiInstance.dashAuthTokenPost(
    cFTurnstileResponse,
    username,
    password
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **cFTurnstileResponse** | [**string**] |  | defaults to undefined|
| **username** | [**string**] |  | defaults to undefined|
| **password** | [**string**] |  | defaults to undefined|


### Return type

**DashAuthTokenPost200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: application/x-www-form-urlencoded, application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**415** | Unsupported Media Type |  -  |
|**400** | Bad Request |  -  |
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**406** | Not Acceptable |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashAuthTokenRefreshGet**
> DashAuthTokenPost200Response dashAuthTokenRefreshGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

const { status, data } = await apiInstance.dashAuthTokenRefreshGet();
```

### Parameters
This endpoint does not have any parameters.


### Return type

**DashAuthTokenPost200Response**

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashAuthTokenRevokeGet**
> dashAuthTokenRevokeGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

const { status, data } = await apiInstance.dashAuthTokenRevokeGet();
```

### Parameters
This endpoint does not have any parameters.


### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashClientsGet**
> DashClientsGet200Response dashClientsGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

const { status, data } = await apiInstance.dashClientsGet();
```

### Parameters
This endpoint does not have any parameters.


### Return type

**DashClientsGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashSearchWallpapersGet**
> DashSearchWallpapersGet200Response dashSearchWallpapersGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let q: string; // (default to undefined)
let after: number; // (optional) (default to undefined)
let limit: number; // (optional) (default to undefined)
let size: Array<string>; //用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 `宽x高`，例如 `1920x1080`。当提供了 `size` 字段后，响应字段 `images` 才出现对应图片地址 (optional) (default to undefined)
let excludeTopicId: number; //排除指定专题下的壁纸 (optional) (default to undefined)

const { status, data } = await apiInstance.dashSearchWallpapersGet(
    q,
    after,
    limit,
    size,
    excludeTopicId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **q** | [**string**] |  | defaults to undefined|
| **after** | [**number**] |  | (optional) defaults to undefined|
| **limit** | [**number**] |  | (optional) defaults to undefined|
| **size** | **Array&lt;string&gt;** | 用于控制图片在该尺寸以内(不会改变图片原始比例)，格式为 &#x60;宽x高&#x60;，例如 &#x60;1920x1080&#x60;。当提供了 &#x60;size&#x60; 字段后，响应字段 &#x60;images&#x60; 才出现对应图片地址 | (optional) defaults to undefined|
| **excludeTopicId** | [**number**] | 排除指定专题下的壁纸 | (optional) defaults to undefined|


### Return type

**DashSearchWallpapersGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**401** | Unauthorized |  -  |
|**200** | OK |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicTopicIdPublishClientIdPost**
> dashTopicTopicIdPublishClientIdPost(dashTopicpublishedPublishedIdPutRequest)


### Example

```typescript
import {
    DashboardApi,
    Configuration,
    DashTopicpublishedPublishedIdPutRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)
let clientId: string; // (default to undefined)
let dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest; //

const { status, data } = await apiInstance.dashTopicTopicIdPublishClientIdPost(
    topicId,
    clientId,
    dashTopicpublishedPublishedIdPutRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **dashTopicpublishedPublishedIdPutRequest** | **DashTopicpublishedPublishedIdPutRequest**|  | |
| **topicId** | [**number**] |  | defaults to undefined|
| **clientId** | [**string**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |
|**201** | Created |  -  |
|**415** | Unsupported Media Type |  -  |
|**409** | Conflict |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicpublishedGet**
> DashTopicpublishedGet200Response dashTopicpublishedGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let page: number; // (optional) (default to undefined)
let pageSize: number; // (optional) (default to undefined)

const { status, data } = await apiInstance.dashTopicpublishedGet(
    page,
    pageSize
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **page** | [**number**] |  | (optional) defaults to undefined|
| **pageSize** | [**number**] |  | (optional) defaults to undefined|


### Return type

**DashTopicpublishedGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicpublishedPublishedIdDelete**
> dashTopicpublishedPublishedIdDelete()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let publishedId: number; // (default to undefined)

const { status, data } = await apiInstance.dashTopicpublishedPublishedIdDelete(
    publishedId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **publishedId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**401** | Unauthorized |  -  |
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**204** | No Content |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicpublishedPublishedIdPut**
> dashTopicpublishedPublishedIdPut(dashTopicpublishedPublishedIdPutRequest)


### Example

```typescript
import {
    DashboardApi,
    Configuration,
    DashTopicpublishedPublishedIdPutRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let publishedId: number; // (default to undefined)
let dashTopicpublishedPublishedIdPutRequest: DashTopicpublishedPublishedIdPutRequest; //

const { status, data } = await apiInstance.dashTopicpublishedPublishedIdPut(
    publishedId,
    dashTopicpublishedPublishedIdPutRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **dashTopicpublishedPublishedIdPutRequest** | **DashTopicpublishedPublishedIdPutRequest**|  | |
| **publishedId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |
|**415** | Unsupported Media Type |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsGet**
> DashTopicsGet200Response dashTopicsGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let page: number; // (optional) (default to undefined)
let pageSize: number; // (optional) (default to undefined)
let excludeWallpaperId: number; //排除含有指定壁纸的专题 (optional) (default to undefined)
let fields: Array<'topics.id' | 'topics.comment' | 'topics.wallpaper_count' | 'topics.published_count' | 'current_page' | 'current_page_size' | 'total'>; // (optional) (default to undefined)

const { status, data } = await apiInstance.dashTopicsGet(
    page,
    pageSize,
    excludeWallpaperId,
    fields
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **page** | [**number**] |  | (optional) defaults to undefined|
| **pageSize** | [**number**] |  | (optional) defaults to undefined|
| **excludeWallpaperId** | [**number**] | 排除含有指定壁纸的专题 | (optional) defaults to undefined|
| **fields** | **Array<&#39;topics.id&#39; &#124; &#39;topics.comment&#39; &#124; &#39;topics.wallpaper_count&#39; &#124; &#39;topics.published_count&#39; &#124; &#39;current_page&#39; &#124; &#39;current_page_size&#39; &#124; &#39;total&#39;>** |  | (optional) defaults to undefined|


### Return type

**DashTopicsGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsPost**
> DashTopicsPost200Response dashTopicsPost(dashTopicsPostRequest)


### Example

```typescript
import {
    DashboardApi,
    Configuration,
    DashTopicsPostRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let dashTopicsPostRequest: DashTopicsPostRequest; //

const { status, data } = await apiInstance.dashTopicsPost(
    dashTopicsPostRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **dashTopicsPostRequest** | **DashTopicsPostRequest**|  | |


### Return type

**DashTopicsPost200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**415** | Unsupported Media Type |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsTopicIdDelete**
> dashTopicsTopicIdDelete()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)

const { status, data } = await apiInstance.dashTopicsTopicIdDelete(
    topicId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **topicId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**204** | No Content |  -  |
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsTopicIdPut**
> DashTopicsPost200Response dashTopicsTopicIdPut(dashTopicsPostRequest)


### Example

```typescript
import {
    DashboardApi,
    Configuration,
    DashTopicsPostRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)
let dashTopicsPostRequest: DashTopicsPostRequest; //

const { status, data } = await apiInstance.dashTopicsTopicIdPut(
    topicId,
    dashTopicsPostRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **dashTopicsPostRequest** | **DashTopicsPostRequest**|  | |
| **topicId** | [**number**] |  | defaults to undefined|


### Return type

**DashTopicsPost200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**415** | Unsupported Media Type |  -  |
|**400** | Bad Request |  -  |
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**404** | Not Found |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsTopicIdSuggestionsTagsGet**
> DashTopicsTopicIdSuggestionsTagsGet200Response dashTopicsTopicIdSuggestionsTagsGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)

const { status, data } = await apiInstance.dashTopicsTopicIdSuggestionsTagsGet(
    topicId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **topicId** | [**number**] |  | defaults to undefined|


### Return type

**DashTopicsTopicIdSuggestionsTagsGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsTopicIdWallpapersGet**
> DashTopicsTopicIdWallpapersGet200Response dashTopicsTopicIdWallpapersGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)
let page: number; // (optional) (default to undefined)
let pageSize: number; // (optional) (default to undefined)

const { status, data } = await apiInstance.dashTopicsTopicIdWallpapersGet(
    topicId,
    page,
    pageSize
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **topicId** | [**number**] |  | defaults to undefined|
| **page** | [**number**] |  | (optional) defaults to undefined|
| **pageSize** | [**number**] |  | (optional) defaults to undefined|


### Return type

**DashTopicsTopicIdWallpapersGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsTopicIdWallpapersWallpaperIdDelete**
> dashTopicsTopicIdWallpapersWallpaperIdDelete()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)
let wallpaperId: number; // (default to undefined)

const { status, data } = await apiInstance.dashTopicsTopicIdWallpapersWallpaperIdDelete(
    topicId,
    wallpaperId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **topicId** | [**number**] |  | defaults to undefined|
| **wallpaperId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |
|**204** | No Content |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashTopicsTopicIdWallpapersWallpaperIdPut**
> dashTopicsTopicIdWallpapersWallpaperIdPut()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let topicId: number; // (default to undefined)
let wallpaperId: number; // (default to undefined)

const { status, data } = await apiInstance.dashTopicsTopicIdWallpapersWallpaperIdPut(
    topicId,
    wallpaperId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **topicId** | [**number**] |  | defaults to undefined|
| **wallpaperId** | [**number**] |  | defaults to undefined|


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |
|**401** | Unauthorized |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashUserinfoGet**
> DashUserinfoGet200Response dashUserinfoGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

const { status, data } = await apiInstance.dashUserinfoGet();
```

### Parameters
This endpoint does not have any parameters.


### Return type

**DashUserinfoGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**401** | Unauthorized |  -  |
|**200** | OK |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashWallpapersGet**
> DashWallpapersGet200Response dashWallpapersGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let page: number; // (optional) (default to undefined)
let pageSize: number; // (optional) (default to undefined)
let fields: Array<'wallpapers.id' | 'wallpapers.images' | 'wallpapers.related_topic_count' | 'wallpapers.tags.name' | 'current_page' | 'current_page_size' | 'total'>; // (optional) (default to undefined)
let sizedFor: 'desktop' | 'mobile'; //[filter] 根据尺寸进行过滤 (optional) (default to undefined)

const { status, data } = await apiInstance.dashWallpapersGet(
    page,
    pageSize,
    fields,
    sizedFor
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **page** | [**number**] |  | (optional) defaults to undefined|
| **pageSize** | [**number**] |  | (optional) defaults to undefined|
| **fields** | **Array<&#39;wallpapers.id&#39; &#124; &#39;wallpapers.images&#39; &#124; &#39;wallpapers.related_topic_count&#39; &#124; &#39;wallpapers.tags.name&#39; &#124; &#39;current_page&#39; &#124; &#39;current_page_size&#39; &#124; &#39;total&#39;>** |  | (optional) defaults to undefined|
| **sizedFor** | [**&#39;desktop&#39; | &#39;mobile&#39;**]**Array<&#39;desktop&#39; &#124; &#39;mobile&#39;>** | [filter] 根据尺寸进行过滤 | (optional) defaults to undefined|


### Return type

**DashWallpapersGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**401** | Unauthorized |  -  |
|**200** | OK |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **dashWallpapersWallpaperIdTopicsGet**
> DashWallpapersWallpaperIdTopicsGet200Response dashWallpapersWallpaperIdTopicsGet()


### Example

```typescript
import {
    DashboardApi,
    Configuration
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let wallpaperId: number; // (default to undefined)

const { status, data } = await apiInstance.dashWallpapersWallpaperIdTopicsGet(
    wallpaperId
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **wallpaperId** | [**number**] |  | defaults to undefined|


### Return type

**DashWallpapersWallpaperIdTopicsGet200Response**

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: Not defined
 - **Accept**: application/json


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**401** | Unauthorized |  -  |
|**404** | Not Found |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **postUploadWallpapers**
> postUploadWallpapers(postUploadWallpapersRequest)


### Example

```typescript
import {
    DashboardApi,
    Configuration,
    PostUploadWallpapersRequest
} from './api';

const configuration = new Configuration();
const apiInstance = new DashboardApi(configuration);

let postUploadWallpapersRequest: PostUploadWallpapersRequest; //

const { status, data } = await apiInstance.postUploadWallpapers(
    postUploadWallpapersRequest
);
```

### Parameters

|Name | Type | Description  | Notes|
|------------- | ------------- | ------------- | -------------|
| **postUploadWallpapersRequest** | **PostUploadWallpapersRequest**|  | |


### Return type

void (empty response body)

### Authorization

[access_token](../README.md#access_token)

### HTTP request headers

 - **Content-Type**: application/json
 - **Accept**: Not defined


### HTTP response details
| Status code | Description | Response headers |
|-------------|-------------|------------------|
|**200** | OK |  -  |
|**403** | Forbidden |  -  |
|**401** | Unauthorized |  -  |
|**415** | Unsupported Media Type |  -  |
|**400** | Bad Request |  -  |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

