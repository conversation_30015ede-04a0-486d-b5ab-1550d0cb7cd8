<script setup lang="tsx">
import { <PERSON>umn, Button, useToast, Image, Tag } from 'primevue'
import { ref, watch } from 'vue'

import { api } from '@/api'
import type {
  DashTopicsGet200Response,
  DashTopicsGet200ResponseTopicsInner,
  DashWallpapersGet200ResponseWallpapersInner,
} from '@/api-client'
import PagingTable from '@/components/PagingTable.vue'
import { useConfirm } from '@/components/primevue'

import { useAddingTopicForm } from './hooks'

const confirm = useConfirm()
const toast = useToast()

interface Props {
  wallpaper: DashWallpapersGet200ResponseWallpapersInner
}
const props = defineProps<Props>()
const emit = defineEmits<{
  /**
   * 壁纸添加成功时事件
   */
  update: []
}>()

const viewData = ref<DashTopicsGet200Response>()
const currentPage = ref(1)
const tableLoading = ref(true)
function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicsGet(currentPage.value, 5, props.wallpaper.id, [
      'topics.id',
      'topics.comment',
      'total',
      'current_page',
      'current_page_size',
      'topics.published_count',
      'topics.wallpaper_count',
    ])
    .then((res) => {
      viewData.value = res.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
watch(
  currentPage,
  () => {
    updateViewData()
  },
  { immediate: true },
)

const addingTopicForm = useAddingTopicForm()
function handleAddingTopic() {
  addingTopicForm.open({
    onSuccess() {
      if (currentPage.value === 1) {
        updateViewData()
      } else {
        currentPage.value = 1
      }
    },
  })
}
</script>
<template>
  <div class="flex gap-8">
    <div class="self-center">
      <Image
        imageClass="w-48 object-contain"
        :src="props.wallpaper.images!.default"
        preview
      ></Image>
      <div class="mt-4 flex gap-2">
        <Tag v-for="tag in props.wallpaper.tags" :key="tag.name" :value="tag.name"></Tag>
      </div>
    </div>

    <div class="flex flex-col">
      <div class="ml-auto">
        <Button label="新增专题" size="small" icon="pi pi-plus" @click="handleAddingTopic" />
      </div>
      <PagingTable
        v-model:current-page="currentPage"
        :loading="tableLoading"
        :value="viewData?.topics"
        :rows="viewData?.current_page_size"
        :total-records="viewData?.total"
        empty-text="没有可添加的专题"
      >
        <Column header="专题备注" field="comment"></Column>
        <Column header="壁纸" field="wallpaper_count"></Column>
        <Column header="已发布" field="published_count"></Column>
        <Column>
          <template #body="slotProps: { data: DashTopicsGet200ResponseTopicsInner }">
            <Button
              icon="pi pi-plus"
              rounded
              size="small"
              @click="
                () => {
                  confirm.require({
                    message: `是否添加到专题 [${slotProps.data.comment}]`,
                    accept: () => {
                      api
                        .dashTopicsTopicIdWallpapersWallpaperIdPut(
                          slotProps.data.id!,
                          props.wallpaper.id!,
                        )
                        .then(() => {
                          updateViewData()
                          toast.add({
                            summary: '壁纸已成功添加到专题',
                            life: 3000,
                            severity: 'success',
                          })
                          emit('update')
                        })
                    },
                  })
                }
              "
            ></Button>
          </template>
        </Column>
      </PagingTable>
    </div>
  </div>
</template>
