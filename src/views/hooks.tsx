import { useDialog } from 'primevue'

import { api } from '@/api'

import TopicFormView from './TopicFormView.vue'

export function useAddingTopicForm() {
  const dialog = useDialog()

  return {
    open: (option: { onSuccess: () => void }) => {
      const dialogInstance = dialog.open(
        <TopicFormView
          onSubmit={(values) => {
            api.dashTopicsPost(values).then(() => {
              option.onSuccess()
              dialogInstance.close()
            })
          }}
          onCancel={() => {
            dialogInstance.close()
          }}
        />,
        {
          props: {
            header: '新增专题',
            modal: true,
            style: { minWidth: '30rem' },
          },
        },
      )
    },
  }
}
