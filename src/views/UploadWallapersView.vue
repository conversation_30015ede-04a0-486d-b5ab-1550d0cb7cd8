<script setup lang="ts">
import type { AxiosError } from 'axios'
import Button from 'primevue/button'
import Textarea from 'primevue/textarea'
import { useToast } from 'primevue/usetoast'
import { ref } from 'vue'

import { api } from '@/api'

const inputValue = ref('')
const isUploading = ref(false)
const toast = useToast()

function handleSubmit() {
  isUploading.value = true

  const urls: URL[] = []
  inputValue.value.split('\n').forEach((item) => {
    try {
      urls.push(new URL(item))
    } catch {
      // ignore
    }
  })
  api
    .postUploadWallpapers(
      { urls: urls.map((item) => item.toString()) },
      { skipCheckStatusCodes: [400] },
    )
    .then(() => {
      inputValue.value = ''
    })
    .catch((err: AxiosError) => {
      if (err.response?.status === 400) {
        toast.add({
          severity: 'error',
          summary: '参数错误，请检查！',
          life: 3000,
        })
      }
    })
    .finally(() => {
      isUploading.value = false
    })
}
</script>

<template>
  <div class="space-y-2">
    <Textarea
      v-model="inputValue"
      placeholder="请输入壁纸链接，多个链接请换行"
      fluid
      :rows="10"
      :disabled="isUploading"
    />
    <div class="flex justify-center">
      <Button
        class="min-w-1/3"
        icon="pi pi-upload"
        label="提 交"
        :loading="isUploading"
        :disabled="!inputValue.trim()"
        @click="handleSubmit"
      >
      </Button>
    </div>
  </div>
</template>
