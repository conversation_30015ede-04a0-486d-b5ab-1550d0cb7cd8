<script lang="ts" setup>
import { Dialog } from 'primevue'
import Column from 'primevue/column'
import SplitButton from 'primevue/splitbutton'
import { onBeforeMount, ref, watch } from 'vue'

import { api } from '@/api'
import type {
  DashTopicpublishedGet200Response,
  DashTopicpublishedGet200ResponsePublishedInner,
} from '@/api-client'
import PagingTable from '@/components/PagingTable.vue'
import { useConfirm } from '@/components/primevue'
import TopicPublishFormView from '@/views/TopicPublishFormView.vue'

const confirm = useConfirm()

const viewData = ref<DashTopicpublishedGet200Response>()
const tableLoading = ref(true)
const currentPage = ref(1)
function updateViewData() {
  tableLoading.value = true
  api
    .dashTopicpublishedGet(currentPage.value, 20)
    .then((resp) => {
      viewData.value = resp.data
    })
    .finally(() => {
      tableLoading.value = false
    })
}
onBeforeMount(() => {
  updateViewData()
})
watch(currentPage, () => {
  updateViewData()
})

const editingDialogVisible = ref(false)
const currentEditingData = ref<DashTopicpublishedGet200ResponsePublishedInner>()
function handleEditPublished(values: { clientId: string; title: string }) {
  const topic = currentEditingData.value
  if (!topic) return
  api.dashTopicpublishedPublishedIdPut(topic.id, { title: values.title }).then(() => {
    updateViewData()
    editingDialogVisible.value = false
  })
}
</script>

<template>
  <div>
    <PagingTable
      :value="viewData?.published"
      :rows="viewData?.current_page_size"
      :total-records="viewData?.total"
      :loading="tableLoading"
    >
      <Column field="id" header="ID"></Column>
      <Column field="title" header="标题"></Column>
      <Column field="topic.comment" header="描述"></Column>
      <Column field="client.name" header="客户端"></Column>
      <Column header="发布时间">
        <template #body="slotProps: { data: DashTopicpublishedGet200ResponsePublishedInner }">
          {{ new Date(slotProps.data.published_at).toLocaleString() }}
        </template>
      </Column>
      <Column header="操作">
        <template #body="slotProps: { data: DashTopicpublishedGet200ResponsePublishedInner }">
          <div class="space-x-2">
            <SplitButton
              label="编辑"
              size="small"
              icon="pi pi-pencil"
              :model="[
                {
                  label: '撤销发布',
                  icon: 'pi pi-times',
                  command(event) {
                    confirm.require({
                      message: '确定要撤销吗？',
                      accept: () => {
                        api.dashTopicpublishedPublishedIdDelete(slotProps.data.id).then(() => {
                          updateViewData()
                        })
                      },
                    })
                  },
                },
              ]"
              @click="
                () => {
                  currentEditingData = slotProps.data
                  editingDialogVisible = true
                }
              "
            />
          </div>
        </template>
      </Column>
    </PagingTable>
  </div>

  <Dialog
    v-model:visible="editingDialogVisible"
    modal
    header="编辑已发布专题"
    :style="{ width: '30rem' }"
  >
    <TopicPublishFormView
      disabled-client-id
      :initialValues="{ clientId: currentEditingData!.client.id, title: currentEditingData!.title }"
      save-label="保 存"
      save-icon="pi pi-check"
      @save="handleEditPublished"
    />
  </Dialog>
</template>
