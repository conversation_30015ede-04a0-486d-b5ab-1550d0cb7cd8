import { api } from '@/api'
import type { DashUserinfoGet200Response } from '@/api-client'

class User {
  private info: DashUserinfoGet200Response | undefined

  async hasPermission(operationId: string): Promise<boolean> {
    if (!this.info) {
      const res = await api.dashUserinfoGet()
      this.info = res.data
    }
    return this.info.is_superuser || this.info.permissions!.includes(operationId)
  }
}

export const user = new User()
