import { createRouter, createWebH<PERSON>ory, RouterView, type RouteRecordRaw } from 'vue-router'

import { api } from '@/api'
import AppLayout from '@/layout/AppLayout.vue'
import { user } from '@/modules/user'
import LoginView from '@/views/LoginView.vue'

export interface RouteMeta {
  label?: string
  icon?: string
  hidden?: boolean
  hasPermission?: () => Promise<boolean>
}

export const RouteName = {
  Root: Symbol(),
  Login: Symbol(),
  TopicWallpapers: Symbol(),
  TopicPublished: Symbol(),
}

export const menuRoutes: RouteRecordRaw[] = [
  {
    path: '/wallpapers',
    component: RouterView,
    meta: {
      label: '壁纸',
    },
    children: [
      {
        path: '',
        component: () => import('@/views/WallpaperListView.vue'),
        meta: {
          label: '壁纸列表',
          icon: 'pi pi-image',
        } satisfies RouteMeta,
      },
      {
        path: 'upload',
        component: () => import('@/views/UploadWallapersView.vue'),
        meta: {
          label: '上传壁纸',
          icon: 'pi pi-upload',
          hasPermission: () => {
            return user.hasPermission(api.postUploadWallpapers.name)
          },
        } satisfies RouteMeta,
      },
    ],
  },

  {
    path: '/topics',
    component: RouterView,
    meta: {
      label: '专题',
    } satisfies RouteMeta,
    children: [
      {
        path: '',
        component: () => import('@/views/TopicListView.vue'),
        meta: {
          label: '专题管理',
          icon: 'pi pi-th-large',
        },
      },
      {
        name: RouteName.TopicWallpapers,
        path: ':id/wallpapers',
        component: () => import('@/views/TopicWallpaperListView.vue'),
        meta: {
          label: '专题壁纸',
          hidden: true,
        } satisfies RouteMeta,
      },
      {
        name: RouteName.TopicPublished,
        path: 'published',
        component: () => import('@/views/TopicPublisedListView.vue'),
        meta: {
          label: '发布管理',
          icon: 'pi pi-send',
        } satisfies RouteMeta,
      },
    ],
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: AppLayout,
      name: RouteName.Root,
      children: menuRoutes,
    },
    { path: '/login', name: RouteName.Login, component: LoginView },
  ],
})

export default router

router.beforeEach(async (to) => {
  const meta = <RouteMeta>to.meta
  if (meta.hasPermission && !(await meta.hasPermission())) {
    return false // TODO 导航到 403 页面
  }
})
