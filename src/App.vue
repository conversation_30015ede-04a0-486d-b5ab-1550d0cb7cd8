<script setup lang="ts">
import type { AxiosResponse } from 'axios'
import { useToast } from 'primevue'
import ConfirmDialog from 'primevue/confirmdialog'
import DynamicDialog from 'primevue/dynamicdialog'
import Toast from 'primevue/toast'
import { onMounted } from 'vue'
import { RouterView } from 'vue-router'

import { API_ERROR_EVENT } from './variables'

const toast = useToast()
onMounted(() => {
  window.addEventListener(API_ERROR_EVENT, (event: Event) => {
    const { detail } = event as CustomEvent<AxiosResponse>

    if (detail.status >= 500) {
      toast.add({
        severity: 'warn',
        summary: '服务器抽筋了！',
        detail: '服务器遇到了一些问题，请稍后再试。',
      })
    } else {
      toast.add({
        severity: 'error',
        summary: `${detail.status} ${detail.statusText}`,
        detail: detail.data?.message,
      })
    }
  })
})
</script>

<template>
  <RouterView />
  <Toast />
  <ConfirmDialog />
  <DynamicDialog />
</template>

<style scoped></style>
