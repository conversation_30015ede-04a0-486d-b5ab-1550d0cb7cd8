import { fileURLToPath, URL } from 'node:url'

import tailwindcss from '@tailwindcss/vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { defineConfig, loadEnv } from 'vite'
import vueDevTools from 'vite-plugin-vue-devtools'

function getBuildTime() {
  const now = new Date()
  // UTC 时间 + 8 小时 = 北京时间
  const beijingTime = new Date(now.getTime() + 8 * 60 * 60 * 1000)

  const pad = (n: number) => n.toString().padStart(2, '0')
  return `${beijingTime.getUTCFullYear()}${pad(beijingTime.getUTCMonth() + 1)}${pad(beijingTime.getUTCDate())}${pad(beijingTime.getUTCHours())}${pad(beijingTime.getUTCMinutes())}`
}

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, '.')
  return {
    plugins: [vue(), vueDevTools(), tailwindcss(), vueJsx()],
    define: {
      __BUILD_TIME__: JSON.stringify(getBuildTime()),
    },
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    server: {
      proxy: {
        '/api': {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite(path) {
            return path.replace(/^\/api/, '')
          },
        },
      },
    },
  }
})
